<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Governorate;
use App\Models\Transaction;
use App\Http\Resources\TransactionResource;
use App\Http\Requests\StoreTransactionRequest;
use App\Repositories\TransactionRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use App\Models\Subscription;
use App\Http\Resources\SubscriptionResource;
use App\Models\AffectationAgent;
use App\Models\Periodicity;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TransactionController extends Controller
{
    private TransactionRepository $repository;

    public function __construct(TransactionRepository $repository)
    {
        $this->repository = $repository;
        $this->authorizeResource(Transaction::class, 'transaction', [
            'except' => ['getPreviousDayTransactions']
        ]);
    }


    public function processPayment(StoreTransactionRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $admin = Auth::guard('api')->user();

            $affectation = AffectationAgent::where('id_agent', $admin->id)
                ->orderBy('created_at', 'desc')
                ->first();

            $salePointId = $affectation?->id_sale_point;
            
            $transactionData = $request->input('transaction');
            $transactionData['employee_id'] = $admin->id;
            $transactionData['sale_point_id'] = $salePointId;
            $transactionData['id_sale_period'] = $affectation?->id_sale_period;

            $transaction = $this->repository->create($transactionData);
            $subscription = Subscription::findOrFail($request->input('subscription.id'));

            $paymentMethodId = $transaction->payment_method_id;
            $paymentDate = $transaction->payment_date;

            $isRenewal = $request->has('subscription.id_parent') && $request->input('subscription.id_parent');
            $startDate = Carbon::parse($paymentDate);

            if ($isRenewal) {
                $parentId = $request->input('subscription.id_parent');
                $parentSubscription = Subscription::find($parentId);

                if ($parentSubscription && $parentSubscription->end_date) {
                    $startDate = Carbon::parse($parentSubscription->end_date);
                }
            }

            $periodicity = Periodicity::find($subscription->id_periodicity);
            $endDate = null;

            if ($periodicity) {
                switch ($periodicity->periodicity_code) {
                    case 'WEEKLY':
                        $endDate = $startDate->copy()->addWeek();
                        break;
                    case 'MONTHLY':
                        $endDate = $startDate->copy()->addMonth();
                        break;
                    case 'SEMESTRIEL':
                        $endDate = $startDate->copy()->addMonths(6);
                        break;
                    case 'TRIMESTRIEL':
                        $endDate = $startDate->copy()->addMonths(3);
                        break;
                    case 'YEARLY':
                        $endDate = $startDate->copy()->addYear();
                        break;
                    default:
                        $endDate = $startDate->copy()->addMonth();
                }
            }

            $isSocialAffair = $request->input('subscription.is_social_affair');


            if ($isSocialAffair) {
                $governorateId = $request->input('transaction.governorate_id');

                $amount = $transaction->amount;

                $deductionSuccess = $this->deductFromPurchaseAmount($governorateId, $amount);

                if (!$deductionSuccess) {
                    DB::rollBack();
                    return response()->json([
                        'message' => 'Error processing payment',
                        'error' => 'Insufficient funds in governorate purchase amount'
                    ], 400);
                }
            }

            $subscription->update([
                'status' => $request->input('subscription.status'),
                'id_payment_method' => $paymentMethodId,
                'is_social_affair' =>  $isSocialAffair,
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate ? $endDate->format('Y-m-d') : null
            ]);

            DB::commit();

            return response()->json([
                'message' => 'Payment processed successfully',
                'data' => [
                    'transaction' => new TransactionResource($transaction),
                    'subscription' => new SubscriptionResource($subscription)
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error processing payment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getByClient(int $clientId): AnonymousResourceCollection
    {
        return TransactionResource::collection(
            $this->repository->findWhere(['client_id' => $clientId])
        );
    }

    public function getBySubscription(int $subscriptionId): AnonymousResourceCollection
    {
        return TransactionResource::collection(
            $this->repository->findWhere(['subscription_id' => $subscriptionId])
        );
    }

    public function getByPaymentMethod(int $paymentMethodId): AnonymousResourceCollection
    {
        return TransactionResource::collection(
            $this->repository->findWhere(['payment_method_id' => $paymentMethodId])
        );
    }

    public function getBySalePoint(int $salePointId): AnonymousResourceCollection
    {
        return TransactionResource::collection(
            $this->repository->findWhere(['sale_point_id' => $salePointId])
        );
    }

    public function getByEmployee(int $employeeId): AnonymousResourceCollection
    {
        return TransactionResource::collection(
            $this->repository->findWhere(['employee_id' => $employeeId])
        );
    }

    public function getByStatus(string $status): AnonymousResourceCollection
    {
        return TransactionResource::collection(
            $this->repository->findWhere(['status' => $status])
        );
    }

    public function getPreviousDayTransactions(): JsonResponse
    {
        try {
            $yesterday = Carbon::yesterday();
            $yesterdayDate = $yesterday->format('Y-m-d');

            $transactions = $this->repository
                ->with([
                    'subscription',
                    'client',
                    'paymentMethod',
                    'salePoint',
                    'employee'
                ])
                ->whereDate('payment_date', $yesterdayDate)
                ->orderBy('payment_date', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'date' => $yesterdayDate,
                'total_transactions' => $transactions->count(),
                'total_amount' => $transactions->sum('amount'),
                'data' => TransactionResource::collection($transactions)
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving previous day transactions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function deductFromPurchaseAmount($governorateId, $amount): bool
    {
        $governorate = Governorate::find($governorateId);

        if (!$governorate || $governorate->purchase_amount < $amount) {
            return false;
        }

        $governorate->purchase_amount -= $amount;

        $purchaseOrders = $governorate->governoratePurchaseOrders()
                                    ->where('status', true)
                                    ->orderBy('created_at', 'asc')
                                    ->get();

        $remainingAmount = $amount;

        foreach ($purchaseOrders as $order) {
            if ($remainingAmount <= 0) {
                break;
            }

            if ($order->current_amount >= $remainingAmount) {
                $order->current_amount -= $remainingAmount;
                $remainingAmount = 0;
            } else {
                $remainingAmount -= $order->current_amount;
                $order->current_amount = 0;
            }

            if ($order->current_amount == 0) {
                $order->status = false;
            }

            $order->save();
        }
        $governorate->save();
        return true;
    }
}





