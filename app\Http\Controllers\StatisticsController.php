<?php

namespace App\Http\Controllers;

use App\Models\Agency;
use App\Models\Subscription;
use App\Models\SubsType;
use App\Models\Transaction;
use App\Repositories\StatisticsRepository;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StatisticsController extends Controller
{
    private StatisticsRepository $repository;

    public function __construct(StatisticsRepository $repository)
    {
        $this->repository = $repository;
        $this->middleware('jwt.auth');
    }

    public function dashboard(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::now()->subYear();
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::now();

        $totalSubscriptions = $request->has('start_date') || $request->has('end_date')
            ? Subscription::whereBetween('created_at', [$startDate, $endDate])->count()
            : Subscription::count();

        $totalRevenue = $request->has('start_date') || $request->has('end_date')
            ? Transaction::where('status', 'completed')
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->sum('amount')
            : Transaction::where('status', 'completed')->sum('amount');


        $statusCounts = $request->has('start_date') || $request->has('end_date')
            ? Subscription::whereBetween('created_at', [$startDate, $endDate])
                ->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->get()
                ->pluck('count', 'status')
                ->toArray()
            : Subscription::select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->get()
                ->pluck('count', 'status')
                ->toArray();

        $allSubsTypes = SubsType::select('id', 'nom_fr', 'nom_en', 'nom_ar')->get()->toArray();

        $subscriptionsByTypeQuery = $request->has('start_date') || $request->has('end_date')
            ? Subscription::join('subs_types', 'subscriptions.id_subs_type', '=', 'subs_types.id')
                ->whereBetween('subscriptions.created_at', [$startDate, $endDate])
                ->select('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar', DB::raw('count(*) as count'))
                ->groupBy('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar')
                ->get()
                ->keyBy('id')
            : Subscription::join('subs_types', 'subscriptions.id_subs_type', '=', 'subs_types.id')
                ->select('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar', DB::raw('count(*) as count'))
                ->groupBy('subs_types.id', 'subs_types.nom_fr', 'subs_types.nom_en', 'subs_types.nom_ar')
                ->get()
                ->keyBy('id');

        $subscriptionsByType = [];
        foreach ($allSubsTypes as $subsType) {
            $id = $subsType['id'];
            $subscriptionsByType[] = [
                'id' => $id,
                'nom_fr' => $subsType['nom_fr'],
                'nom_en' => $subsType['nom_en'],
                'nom_ar' => $subsType['nom_ar'],
                'count' => $subscriptionsByTypeQuery->has($id) ? $subscriptionsByTypeQuery[$id]->count : 0
            ];
        }

        return response()->json([
            'total_subscriptions' => $totalSubscriptions,
            'total_revenue' => $totalRevenue,
            'subscription_status' => $statusCounts,
            'subscriptions_by_type' => $subscriptionsByType,
        ]);
    }

    public function subscribersByTrip(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|integer',
            'journey' => 'nullable|exists:trips,id',
        ]);

        $query = Subscription::join('trips', 'subscriptions.id_trip', '=', 'trips.id')
            ->join('stations as start_station', 'trips.id_station_start', '=', 'start_station.id')
            ->join('stations as end_station', 'trips.id_station_end', '=', 'end_station.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            });

        if ($request->filled('subscription_type') && $request->input('subscription_type') !== 'all') {
            $query->where('subscriptions.id_subs_type', $request->input('subscription_type'));
        }

        if ($request->filled('start_date')) {
            $query->where('subscriptions.created_at', '>=', $request->input('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->where('subscriptions.created_at', '<=', $request->input('end_date'));
        }

        if ($request->filled('journey') && $request->input('journey') !== 'all') {
            $query->where('subscriptions.id_trip', $request->input('journey'));
        }

        // TODO: Add sale period filter when sale period relationship is clarified
        // if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
        //     $query->where('subscriptions.sale_period_id', $request->input('salePeriod'));
        // }

        $results = $query->select(
                'trips.id as trip_id',
                'trips.nom_fr as trip_name_fr',
                'trips.nom_en as trip_name_en',
                'trips.nom_ar as trip_name_ar',
                DB::raw('COUNT(DISTINCT subscriptions.id) as subscriber_count'),
                DB::raw('COUNT(DISTINCT transactions.payment_id) as transaction_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('trips.id', 'trips.nom_fr', 'trips.nom_en', 'trips.nom_ar')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $lastMonthQuery = Subscription::join('trips', 'subscriptions.id_trip', '=', 'trips.id')
            ->join('stations as start_station', 'trips.id_station_start', '=', 'start_station.id')
            ->join('stations as end_station', 'trips.id_station_end', '=', 'end_station.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            });

        $lastMonthStart = now()->startOfMonth()->format('Y-m-d');
        $lastMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $lastMonthQuery->whereBetween('subscriptions.created_at', [$lastMonthStart, $lastMonthEnd]);

        if ($request->filled('subscription_type') && $request->input('subscription_type') !== 'all') {
            $lastMonthQuery->where('subscriptions.id_subs_type', $request->input('subscription_type'));
        }

        if ($request->filled('journey') && $request->input('journey') !== 'all') {
            $lastMonthQuery->where('subscriptions.id_trip', $request->input('journey'));
        }

        $lastMonthResults = $lastMonthQuery->select(
                'trips.id as trip_id',
                'trips.nom_fr as trip_name_fr',
                'trips.nom_en as trip_name_en',
                'trips.nom_ar as trip_name_ar',
                DB::raw('COUNT(DISTINCT subscriptions.id) as subscriber_count'),
                DB::raw('COUNT(DISTINCT transactions.payment_id) as transaction_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('trips.id', 'trips.nom_fr', 'trips.nom_en', 'trips.nom_ar')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $totalAmount = $results->sum('total_amount');
        $totalSubscribers = $results->sum('subscriber_count');
        $totalTransactions = $results->sum('transaction_count');
        $lastMonthTotalAmount = $lastMonthResults->sum('total_amount');
        $lastMonthTotalSubscribers = $lastMonthResults->sum('subscriber_count');
        $lastMonthTotalTransactions = $lastMonthResults->sum('transaction_count');

        $lastMonthData = [];
        foreach ($lastMonthResults as $item) {
            $lastMonthData[$item->trip_id] = $item;
        }

        return response()->json([
            'data' => $results->map(function($item) use ($lastMonthData) {
                $lastMonthItem = $lastMonthData[$item->trip_id] ?? null;
                return [
                    'trip_id' => $item->trip_id,
                    'nom_fr' => $item->trip_name_fr,
                    'nom_en' => $item->trip_name_en,
                    'nom_ar' => $item->trip_name_ar,
                    'all_times' => [
                        'subscriber_count' => (int)$item->subscriber_count,
                        'transaction_count' => (int)$item->transaction_count,
                        'total_amount' => (float)$item->total_amount
                    ],
                    'last_month' => [
                        'subscriber_count' => $lastMonthItem ? (int)$lastMonthItem->subscriber_count : 0,
                        'transaction_count' => $lastMonthItem ? (int)$lastMonthItem->transaction_count : 0,
                        'total_amount' => $lastMonthItem ? (float)$lastMonthItem->total_amount : 0
                    ]
                ];
            }),
            'summary' => [
                'all_times' => [
                    'total_subscribers' => (int)$totalSubscribers,
                    'total_transactions' => (int)$totalTransactions,
                    'total_amount' => (float)$totalAmount,
                    'total_trips' => $results->count()
                ],
                'last_month' => [
                    'total_subscribers' => (int)$lastMonthTotalSubscribers,
                    'total_transactions' => (int)$lastMonthTotalTransactions,
                    'total_amount' => (float)$lastMonthTotalAmount,
                    'total_trips' => $lastMonthResults->count()
                ]
            ]
        ]);
    }

    public function subscribersByEstablishment(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|integer',
            'establishment' => 'nullable|exists:establishments,id',
        ]);

        $query = Subscription::join('clients', 'subscriptions.id_client', '=', 'clients.id')
            ->leftJoin('establishments', 'clients.id_establishment', '=', 'establishments.id')
            ->join('subs_types', 'subscriptions.id_subs_type', '=', 'subs_types.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            })
            ->where('subs_types.is_student', true);

        if ($request->filled('subscription_type') && $request->input('subscription_type') !== 'all') {
            $query->where('subscriptions.id_subs_type', $request->input('subscription_type'));
        }

        if ($request->filled('start_date')) {
            $query->where('subscriptions.created_at', '>=', $request->input('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->where('subscriptions.created_at', '<=', $request->input('end_date'));
        }

        if ($request->filled('establishment') && $request->input('establishment') !== 'all') {
            $query->where('clients.id_establishment', $request->input('establishment'));
        }

        // TODO: Add sale period filter when sale period relationship is clarified
        // if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
        //     $query->where('subscriptions.sale_period_id', $request->input('salePeriod'));
        // }

        $results = $query->select(
                'establishments.id as establishment_id',
                'establishments.nom_fr as establishment_name_fr',
                'establishments.nom_en as establishment_name_en',
                'establishments.nom_ar as establishment_name_ar',
                DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('establishments.id', 'establishments.nom_fr', 'establishments.nom_en', 'establishments.nom_ar')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $lastMonthQuery = Subscription::join('clients', 'subscriptions.id_client', '=', 'clients.id')
            ->leftJoin('establishments', 'clients.id_establishment', '=', 'establishments.id')
            ->join('subs_types', 'subscriptions.id_subs_type', '=', 'subs_types.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            })
            ->where('subs_types.is_student', true);

        $lastMonthStart = now()->startOfMonth()->format('Y-m-d');
        $lastMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $lastMonthQuery->whereBetween('subscriptions.created_at', [$lastMonthStart, $lastMonthEnd]);

        if ($request->filled('subscription_type') && $request->input('subscription_type') !== 'all') {
            $lastMonthQuery->where('subscriptions.id_subs_type', $request->input('subscription_type'));
        }

        if ($request->filled('establishment') && $request->input('establishment') !== 'all') {
            $lastMonthQuery->where('clients.id_establishment', $request->input('establishment'));
        }

        $lastMonthResults = $lastMonthQuery->select(
                'establishments.id as establishment_id',
                'establishments.nom_fr as establishment_name_fr',
                'establishments.nom_en as establishment_name_en',
                'establishments.nom_ar as establishment_name_ar',
                DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('establishments.id', 'establishments.nom_fr', 'establishments.nom_en', 'establishments.nom_ar')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $totalAmount = $results->sum('total_amount');
        $totalSubscribers = $results->sum('subscriber_count');
        $lastMonthTotalAmount = $lastMonthResults->sum('total_amount');
        $lastMonthTotalSubscribers = $lastMonthResults->sum('subscriber_count');

        $lastMonthData = [];
        foreach ($lastMonthResults as $item) {
            $lastMonthData[$item->establishment_id] = $item;
        }

        return response()->json([
            'data' => $results->map(function($item) use ($lastMonthData) {
                $lastMonthItem = $lastMonthData[$item->establishment_id] ?? null;
                return [
                    'establishment_id' => $item->establishment_id,
                    'nom_fr' => $item->establishment_name_fr ?: 'Non spécifié',
                    'nom_en' => $item->establishment_name_en ?: 'Not specified',
                    'nom_ar' => $item->establishment_name_ar ?: 'غير محدد',
                    'all_times' => [
                        'subscriber_count' => (int)$item->subscriber_count,
                        'total_amount' => (float)$item->total_amount
                    ],
                    'last_month' => [
                        'subscriber_count' => $lastMonthItem ? (int)$lastMonthItem->subscriber_count : 0,
                        'total_amount' => $lastMonthItem ? (float)$lastMonthItem->total_amount : 0
                    ]
                ];
            }),
            'summary' => [
                'all_times' => [
                    'total_subscribers' => (int)$totalSubscribers,
                    'total_amount' => (float)$totalAmount,
                    'total_establishments' => $results->count()
                ],
                'last_month' => [
                    'total_subscribers' => (int)$lastMonthTotalSubscribers,
                    'total_amount' => (float)$lastMonthTotalAmount,
                    'total_establishments' => $lastMonthResults->count()
                ]
            ]
        ]);
    }

    public function subscribersByKilometricRangePerLine(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|integer',
            'lineId' => 'nullable|exists:lines,id',
        ]);

        $query = Subscription::join('trips', 'subscriptions.id_trip', '=', 'trips.id')
            ->join('lines', 'trips.id_line', '=', 'lines.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            });

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $query->where('subscriptions.id_subs_type', $request->input('subscriptionType'));
        }

        if ($request->filled('startDate')) {
            $query->where('subscriptions.start_date', '>=', $request->input('startDate'));
        }

        if ($request->filled('endDate')) {
            $query->where('subscriptions.start_date', '<=', $request->input('endDate'));
        }

        if ($request->filled('lineId') && $request->input('lineId') !== 'all') {
            $query->where('trips.id_line', $request->input('lineId'));
        }

        $results = $query->select(
                'lines.id as line_id',
                'lines.nom_fr as line_name_fr',
                'lines.nom_en as line_name_en',
                'lines.nom_ar as line_name_ar',
                'lines.CODE_LINE as line_code',
                DB::raw('FLOOR(trips.number_of_km / 5) * 5 as km_range_start'),
                DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('lines.id', 'lines.nom_fr', 'lines.nom_en', 'lines.nom_ar', 'lines.CODE_LINE', DB::raw('FLOOR(trips.number_of_km / 5) * 5'))
            ->orderBy('lines.nom_fr')
            ->orderBy(DB::raw('FLOOR(trips.number_of_km / 5) * 5'))
            ->get();

        $lastMonthQuery = Subscription::join('trips', 'subscriptions.id_trip', '=', 'trips.id')
            ->join('lines', 'trips.id_line', '=', 'lines.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            });

        $lastMonthStart = now()->startOfMonth()->format('Y-m-d');
        $lastMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $lastMonthQuery->whereBetween('subscriptions.start_date', [$lastMonthStart, $lastMonthEnd]);

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $lastMonthQuery->where('subscriptions.id_subs_type', $request->input('subscriptionType'));
        }

        if ($request->filled('lineId') && $request->input('lineId') !== 'all') {
            $lastMonthQuery->where('trips.id_line', $request->input('lineId'));
        }

        $lastMonthResults = $lastMonthQuery->select(
                'lines.id as line_id',
                'lines.nom_fr as line_name_fr',
                'lines.nom_en as line_name_en',
                'lines.nom_ar as line_name_ar',
                'lines.CODE_LINE as line_code',
                DB::raw('FLOOR(trips.number_of_km / 5) * 5 as km_range_start'),
                DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('lines.id', 'lines.nom_fr', 'lines.nom_en', 'lines.nom_ar', 'lines.CODE_LINE', DB::raw('FLOOR(trips.number_of_km / 5) * 5'))
            ->orderBy('lines.nom_fr')
            ->orderBy(DB::raw('FLOOR(trips.number_of_km / 5) * 5'))
            ->get();

        $lineStats = [];
        $grandTotalAmount = 0;
        $grandTotalSubscribers = 0;

        foreach ($results as $item) {
            $lineId = $item->line_id;
            $kmRangeEnd = (int)$item->km_range_start + 4;

            if (!isset($lineStats[$lineId])) {
                $lineStats[$lineId] = [
                    'line_id' => $lineId,
                    'line_name_fr' => $item->line_name_fr,
                    'line_name_en' => $item->line_name_en,
                    'line_name_ar' => $item->line_name_ar,
                    'line_code' => $item->line_code,
                    'all_times' => [
                        'kilometric_ranges' => [],
                        'line_total_subscribers' => 0,
                        'line_total_amount' => 0
                    ],
                    'last_month' => [
                        'kilometric_ranges' => [],
                        'line_total_subscribers' => 0,
                        'line_total_amount' => 0
                    ]
                ];
            }

            $rangeData = [
                'kilometric_range' => $item->km_range_start . ' - ' . $kmRangeEnd . ' km',
                'km_range_start' => (int)$item->km_range_start,
                'km_range_end' => $kmRangeEnd,
                'subscriber_count' => (int)$item->subscriber_count,
                'total_amount' => (float)$item->total_amount,
                'average_amount_per_subscriber' => $item->subscriber_count > 0
                    ? round((float)$item->total_amount / (int)$item->subscriber_count, 2)
                    : 0
            ];

            $lineStats[$lineId]['all_times']['kilometric_ranges'][] = $rangeData;
            $lineStats[$lineId]['all_times']['line_total_subscribers'] += (int)$item->subscriber_count;
            $lineStats[$lineId]['all_times']['line_total_amount'] += (float)$item->total_amount;

            $grandTotalSubscribers += (int)$item->subscriber_count;
            $grandTotalAmount += (float)$item->total_amount;
        }

        $lastMonthGrandTotalAmount = 0;
        $lastMonthGrandTotalSubscribers = 0;

        foreach ($lastMonthResults as $item) {
            $lineId = $item->line_id;
            $kmRangeEnd = (int)$item->km_range_start + 4;

            if (!isset($lineStats[$lineId])) {
                $lineStats[$lineId] = [
                    'line_id' => $lineId,
                    'line_name_fr' => $item->line_name_fr,
                    'line_name_en' => $item->line_name_en,
                    'line_name_ar' => $item->line_name_ar,
                    'line_code' => $item->line_code,
                    'all_times' => [
                        'kilometric_ranges' => [],
                        'line_total_subscribers' => 0,
                        'line_total_amount' => 0
                    ],
                    'last_month' => [
                        'kilometric_ranges' => [],
                        'line_total_subscribers' => 0,
                        'line_total_amount' => 0
                    ]
                ];
            }

            $rangeData = [
                'kilometric_range' => $item->km_range_start . ' - ' . $kmRangeEnd . ' km',
                'km_range_start' => (int)$item->km_range_start,
                'km_range_end' => $kmRangeEnd,
                'subscriber_count' => (int)$item->subscriber_count,
                'total_amount' => (float)$item->total_amount,
                'average_amount_per_subscriber' => $item->subscriber_count > 0
                    ? round((float)$item->total_amount / (int)$item->subscriber_count, 2)
                    : 0
            ];

            $lineStats[$lineId]['last_month']['kilometric_ranges'][] = $rangeData;
            $lineStats[$lineId]['last_month']['line_total_subscribers'] += (int)$item->subscriber_count;
            $lineStats[$lineId]['last_month']['line_total_amount'] += (float)$item->total_amount;

            $lastMonthGrandTotalSubscribers += (int)$item->subscriber_count;
            $lastMonthGrandTotalAmount += (float)$item->total_amount;
        }

        foreach ($lineStats as &$lineData) {
            $lineData['all_times']['line_total_amount'] = round($lineData['all_times']['line_total_amount'], 2);
            $lineData['all_times']['percentage_of_total_subscribers'] = $grandTotalSubscribers > 0
                ? round(($lineData['all_times']['line_total_subscribers'] / $grandTotalSubscribers) * 100, 2)
                : 0;
            $lineData['all_times']['percentage_of_total_amount'] = $grandTotalAmount > 0
                ? round(($lineData['all_times']['line_total_amount'] / $grandTotalAmount) * 100, 2)
                : 0;
            $lineData['all_times']['average_amount_per_subscriber_line'] = $lineData['all_times']['line_total_subscribers'] > 0
                ? round($lineData['all_times']['line_total_amount'] / $lineData['all_times']['line_total_subscribers'], 2)
                : 0;

            $lineData['last_month']['line_total_amount'] = round($lineData['last_month']['line_total_amount'], 2);
            $lineData['last_month']['percentage_of_total_subscribers'] = $lastMonthGrandTotalSubscribers > 0
                ? round(($lineData['last_month']['line_total_subscribers'] / $lastMonthGrandTotalSubscribers) * 100, 2)
                : 0;
            $lineData['last_month']['percentage_of_total_amount'] = $lastMonthGrandTotalAmount > 0
                ? round(($lineData['last_month']['line_total_amount'] / $lastMonthGrandTotalAmount) * 100, 2)
                : 0;
            $lineData['last_month']['average_amount_per_subscriber_line'] = $lineData['last_month']['line_total_subscribers'] > 0
                ? round($lineData['last_month']['line_total_amount'] / $lineData['last_month']['line_total_subscribers'], 2)
                : 0;

            foreach ($lineData['all_times']['kilometric_ranges'] as &$range) {
                $range['percentage_within_line_subscribers'] = $lineData['all_times']['line_total_subscribers'] > 0
                    ? round(($range['subscriber_count'] / $lineData['all_times']['line_total_subscribers']) * 100, 2)
                    : 0;
                $range['percentage_within_line_amount'] = $lineData['all_times']['line_total_amount'] > 0
                    ? round(($range['total_amount'] / $lineData['all_times']['line_total_amount']) * 100, 2)
                    : 0;
            }

            foreach ($lineData['last_month']['kilometric_ranges'] as &$range) {
                $range['percentage_within_line_subscribers'] = $lineData['last_month']['line_total_subscribers'] > 0
                    ? round(($range['subscriber_count'] / $lineData['last_month']['line_total_subscribers']) * 100, 2)
                    : 0;
                $range['percentage_within_line_amount'] = $lineData['last_month']['line_total_amount'] > 0
                    ? round(($range['total_amount'] / $lineData['last_month']['line_total_amount']) * 100, 2)
                    : 0;
            }
        }

        return response()->json([
            'data' => array_values($lineStats),
            'summary' => [
                'all_times' => [
                    'total_lines' => count($lineStats),
                    'grand_total_subscribers' => $grandTotalSubscribers,
                    'grand_total_amount' => round($grandTotalAmount, 2),
                    'average_subscribers_per_line' => count($lineStats) > 0
                        ? round($grandTotalSubscribers / count($lineStats), 2)
                        : 0,
                    'average_amount_per_line' => count($lineStats) > 0
                        ? round($grandTotalAmount / count($lineStats), 2)
                        : 0
                ],
                'last_month' => [
                    'total_lines' => count($lineStats),
                    'grand_total_subscribers' => $lastMonthGrandTotalSubscribers,
                    'grand_total_amount' => round($lastMonthGrandTotalAmount, 2),
                    'average_subscribers_per_line' => count($lineStats) > 0
                        ? round($lastMonthGrandTotalSubscribers / count($lineStats), 2)
                        : 0,
                    'average_amount_per_line' => count($lineStats) > 0
                        ? round($lastMonthGrandTotalAmount / count($lineStats), 2)
                        : 0
                ]
            ]
        ]);
    }

    public function subscribersByDiscount(Request $request): JsonResponse
    {
        $request->validate([
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'subscriptionType' => 'nullable|exists:subs_types,id',
        ]);

        $query = "
            SELECT
                d.id as discount_id,
                d.nom_fr,
                d.nom_en,
                d.nom_ar,
                d.percentage,
                COUNT(DISTINCT t.payment_id) as nombre_transactions,
                COUNT(DISTINCT s.id) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total
            FROM discounts d
            LEFT JOIN transactions t ON (
                JSON_EXTRACT(t.payment_details, '$.id_discount') = d.id
                AND t.status = 'completed'
            )
            LEFT JOIN subscriptions s ON t.subscription_id = s.id
        ";

        $bindings = [];

        $conditions = [];

        if ($request->filled('startDate')) {
            $conditions[] = "t.payment_date >= ?";
            $bindings[] = $request->input('startDate');
        }

        if ($request->filled('endDate')) {
            $conditions[] = "t.payment_date <= ?";
            $bindings[] = $request->input('endDate');
        }

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $conditions[] = "s.id_subs_type = ?";
            $bindings[] = $request->input('subscriptionType');
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }

        $query .= "
            GROUP BY d.id, d.nom_fr, d.nom_en, d.nom_ar, d.percentage
            ORDER BY d.nom_fr
        ";

        $results = DB::select($query, $bindings);

        $lastMonthQuery = "
            SELECT
                d.id as discount_id,
                d.nom_fr,
                d.nom_en,
                d.nom_ar,
                d.percentage,
                COUNT(DISTINCT t.payment_id) as nombre_transactions,
                COUNT(DISTINCT s.id) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total
            FROM discounts d
            LEFT JOIN transactions t ON (
                JSON_EXTRACT(t.payment_details, '$.id_discount') = d.id
                AND t.status = 'completed'
            )
            LEFT JOIN subscriptions s ON t.subscription_id = s.id
        ";

        $lastMonthStart = now()->startOfMonth()->format('Y-m-d');
        $lastMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $lastMonthConditions = [];
        $lastMonthBindings = [];

        $lastMonthConditions[] = "t.payment_date >= ?";
        $lastMonthBindings[] = $lastMonthStart;
        $lastMonthConditions[] = "t.payment_date <= ?";
        $lastMonthBindings[] = $lastMonthEnd;

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $lastMonthConditions[] = "s.id_subs_type = ?";
            $lastMonthBindings[] = $request->input('subscriptionType');
        }

        $lastMonthQuery .= " WHERE " . implode(' AND ', $lastMonthConditions);
        $lastMonthQuery .= "
            GROUP BY d.id, d.nom_fr, d.nom_en, d.nom_ar, d.percentage
            ORDER BY d.nom_fr
        ";

        $lastMonthResults = DB::select($lastMonthQuery, $lastMonthBindings);

        $totalTransactions = array_sum(array_column($results, 'nombre_transactions'));
        $totalAbonnements = array_sum(array_column($results, 'nombre_abonnements'));
        $totalMontant = array_sum(array_column($results, 'montant_total'));

        $lastMonthTotalTransactions = array_sum(array_column($lastMonthResults, 'nombre_transactions'));
        $lastMonthTotalAbonnements = array_sum(array_column($lastMonthResults, 'nombre_abonnements'));
        $lastMonthTotalMontant = array_sum(array_column($lastMonthResults, 'montant_total'));

        $lastMonthData = [];
        foreach ($lastMonthResults as $item) {
            $lastMonthData[$item->discount_id] = $item;
        }

        return response()->json([
            'data' => array_map(function($item) use ($lastMonthData) {
                $lastMonthItem = $lastMonthData[$item->discount_id] ?? null;
                return [
                    'discount_id' => $item->discount_id,
                    'nom_fr' => $item->nom_fr,
                    'nom_en' => $item->nom_en,
                    'nom_ar' => $item->nom_ar,
                    'percentage' => (float)$item->percentage,
                    'all_times' => [
                        'nombre_transactions' => (int)$item->nombre_transactions,
                        'nombre_abonnements' => (int)$item->nombre_abonnements,
                        'montant_total' => round((float)$item->montant_total, 2)
                    ],
                    'last_month' => [
                        'nombre_transactions' => $lastMonthItem ? (int)$lastMonthItem->nombre_transactions : 0,
                        'nombre_abonnements' => $lastMonthItem ? (int)$lastMonthItem->nombre_abonnements : 0,
                        'montant_total' => $lastMonthItem ? round((float)$lastMonthItem->montant_total, 2) : 0
                    ]
                ];
            }, $results),
            'summary' => [
                'all_times' => [
                    'total_discounts' => count($results),
                    'total_transactions' => $totalTransactions,
                    'total_abonnements' => $totalAbonnements,
                    'total_montant' => round($totalMontant, 2)
                ],
                'last_month' => [
                    'total_discounts' => count($lastMonthResults),
                    'total_transactions' => $lastMonthTotalTransactions,
                    'total_abonnements' => $lastMonthTotalAbonnements,
                    'total_montant' => round($lastMonthTotalMontant, 2)
                ]
            ]
        ]);
    }

    public function revenuesBySalePeriod(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
        ]);

        $query = "
            SELECT
                c.id as campaign_id,
                c.nom_fr as campaign_nom_fr,
                c.nom_en as campaign_nom_en,
                c.nom_ar as campaign_nom_ar,
                sp.id as sale_period_id,
                sp.nom_fr as sale_period_nom_fr,
                sp.nom_en as sale_period_nom_en,
                sp.nom_ar as sale_period_nom_ar,
                sp.date_start,
                sp.date_end,
                COUNT(DISTINCT t.payment_id) as nombre_transactions,
                COUNT(DISTINCT s.id) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total
            FROM sale_periods sp
            JOIN campaigns c ON sp.id_campaign = c.id
            LEFT JOIN transactions t ON sp.id = t.id_sale_period AND t.status = 'completed'
            LEFT JOIN subscriptions s ON t.subscription_id = s.id
        ";

        $bindings = [];
        $conditions = [];

        if ($request->filled('startDate')) {
            $conditions[] = "t.payment_date >= ?";
            $bindings[] = $request->input('startDate');
        }

        if ($request->filled('endDate')) {
            $conditions[] = "t.payment_date <= ?";
            $bindings[] = $request->input('endDate');
        }

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $conditions[] = "s.id_subs_type = ?";
            $bindings[] = $request->input('subscriptionType');
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }

        $query .= "
            GROUP BY c.id, c.nom_fr, c.nom_en, c.nom_ar, sp.id, sp.nom_fr, sp.nom_en, sp.nom_ar, sp.date_start, sp.date_end
            ORDER BY c.nom_fr, sp.date_start
        ";

        $results = DB::select($query, $bindings);

        $lastMonthQuery = "
            SELECT
                c.id as campaign_id,
                c.nom_fr as campaign_nom_fr,
                c.nom_en as campaign_nom_en,
                c.nom_ar as campaign_nom_ar,
                sp.id as sale_period_id,
                sp.nom_fr as sale_period_nom_fr,
                sp.nom_en as sale_period_nom_en,
                sp.nom_ar as sale_period_nom_ar,
                sp.date_start,
                sp.date_end,
                COUNT(DISTINCT t.payment_id) as nombre_transactions,
                COUNT(DISTINCT s.id) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total
            FROM sale_periods sp
            JOIN campaigns c ON sp.id_campaign = c.id
            LEFT JOIN transactions t ON sp.id = t.id_sale_period AND t.status = 'completed'
            LEFT JOIN subscriptions s ON t.subscription_id = s.id
        ";

        $lastMonthStart = now()->startOfMonth()->format('Y-m-d');
        $lastMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $lastMonthConditions = [];
        $lastMonthBindings = [];

        $lastMonthConditions[] = "t.payment_date >= ?";
        $lastMonthBindings[] = $lastMonthStart;
        $lastMonthConditions[] = "t.payment_date <= ?";
        $lastMonthBindings[] = $lastMonthEnd;

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $lastMonthConditions[] = "s.id_subs_type = ?";
            $lastMonthBindings[] = $request->input('subscriptionType');
        }

        $lastMonthQuery .= " WHERE " . implode(' AND ', $lastMonthConditions);
        $lastMonthQuery .= "
            GROUP BY c.id, c.nom_fr, c.nom_en, c.nom_ar, sp.id, sp.nom_fr, sp.nom_en, sp.nom_ar, sp.date_start, sp.date_end
            ORDER BY c.nom_fr, sp.date_start
        ";

        $lastMonthResults = DB::select($lastMonthQuery, $lastMonthBindings);

        $totalTransactions = array_sum(array_column($results, 'nombre_transactions'));
        $totalAbonnements = array_sum(array_column($results, 'nombre_abonnements'));
        $totalMontant = array_sum(array_column($results, 'montant_total'));

        $lastMonthTotalTransactions = array_sum(array_column($lastMonthResults, 'nombre_transactions'));
        $lastMonthTotalAbonnements = array_sum(array_column($lastMonthResults, 'nombre_abonnements'));
        $lastMonthTotalMontant = array_sum(array_column($lastMonthResults, 'montant_total'));

        $lastMonthData = [];
        foreach ($lastMonthResults as $item) {
            $lastMonthData[$item->sale_period_id] = $item;
        }

        return response()->json([
            'data' => array_map(function($item) use ($lastMonthData) {
                $lastMonthItem = $lastMonthData[$item->sale_period_id] ?? null;
                return [
                    'campaign_id' => $item->campaign_id,
                    'campaign_nom_fr' => $item->campaign_nom_fr,
                    'campaign_nom_en' => $item->campaign_nom_en,
                    'campaign_nom_ar' => $item->campaign_nom_ar,
                    'sale_period_id' => $item->sale_period_id,
                    'sale_period_nom_fr' => $item->sale_period_nom_fr,
                    'sale_period_nom_en' => $item->sale_period_nom_en,
                    'sale_period_nom_ar' => $item->sale_period_nom_ar,
                    'date_start' => $item->date_start,
                    'date_end' => $item->date_end,
                    'all_times' => [
                        'nombre_transactions' => (int)$item->nombre_transactions,
                        'nombre_abonnements' => (int)$item->nombre_abonnements,
                        'montant_total' => round((float)$item->montant_total, 2)
                    ],
                    'last_month' => [
                        'nombre_transactions' => $lastMonthItem ? (int)$lastMonthItem->nombre_transactions : 0,
                        'nombre_abonnements' => $lastMonthItem ? (int)$lastMonthItem->nombre_abonnements : 0,
                        'montant_total' => $lastMonthItem ? round((float)$lastMonthItem->montant_total, 2) : 0
                    ]
                ];
            }, $results),
            'summary' => [
                'all_times' => [
                    'total_sale_periods' => count($results),
                    'total_transactions' => $totalTransactions,
                    'total_abonnements' => $totalAbonnements,
                    'total_montant' => round($totalMontant, 2)
                ],
                'last_month' => [
                    'total_sale_periods' => count($lastMonthResults),
                    'total_transactions' => $lastMonthTotalTransactions,
                    'total_abonnements' => $lastMonthTotalAbonnements,
                    'total_montant' => round($lastMonthTotalMontant, 2)
                ]
            ]
        ]);
    }

    public function subscribersByLine(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|integer',
            'lineId' => 'nullable|exists:lines,id',
        ]);

        $query = Subscription::join('trips', 'subscriptions.id_trip', '=', 'trips.id')
            ->join('lines', 'trips.id_line', '=', 'lines.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            });

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $query->where('subscriptions.id_subs_type', $request->input('subscriptionType'));
        }

        if ($request->filled('startDate')) {
            $query->where('subscriptions.created_at', '>=', $request->input('startDate'));
        }

        if ($request->filled('endDate')) {
            $query->where('subscriptions.created_at', '<=', $request->input('endDate'));
        }

        if ($request->filled('lineId') && $request->input('lineId') !== 'all') {
            $query->where('trips.id_line', $request->input('lineId'));
        }

        // TODO: Add sale period filter when sale period relationship is clarified
        // if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
        //     $query->where('subscriptions.sale_period_id', $request->input('salePeriod'));
        // }

        $results = $query->select(
                'lines.id as line_id',
                'lines.nom_fr as line_name_fr',
                'lines.nom_en as line_name_en',
                'lines.nom_ar as line_name_ar',
                'lines.CODE_LINE as line_code',
                DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('lines.id', 'lines.nom_fr', 'lines.nom_en', 'lines.nom_ar', 'lines.CODE_LINE')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $lastMonthQuery = Subscription::join('trips', 'subscriptions.id_trip', '=', 'trips.id')
            ->join('lines', 'trips.id_line', '=', 'lines.id')
            ->leftJoin('transactions', function($join) {
                $join->on('subscriptions.id', '=', 'transactions.subscription_id')
                     ->where('transactions.status', '=', 'completed');
            });

        $lastMonthStart = now()->startOfMonth()->format('Y-m-d');
        $lastMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $lastMonthQuery->whereBetween('subscriptions.created_at', [$lastMonthStart, $lastMonthEnd]);

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $lastMonthQuery->where('subscriptions.id_subs_type', $request->input('subscriptionType'));
        }

        if ($request->filled('lineId') && $request->input('lineId') !== 'all') {
            $lastMonthQuery->where('trips.id_line', $request->input('lineId'));
        }

        $lastMonthResults = $lastMonthQuery->select(
                'lines.id as line_id',
                'lines.nom_fr as line_name_fr',
                'lines.nom_en as line_name_en',
                'lines.nom_ar as line_name_ar',
                'lines.CODE_LINE as line_code',
                DB::raw('COUNT(subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('lines.id', 'lines.nom_fr', 'lines.nom_en', 'lines.nom_ar', 'lines.CODE_LINE')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $totalAmount = $results->sum('total_amount');
        $totalSubscribers = $results->sum('subscriber_count');
        $lastMonthTotalAmount = $lastMonthResults->sum('total_amount');
        $lastMonthTotalSubscribers = $lastMonthResults->sum('subscriber_count');

        $lastMonthData = [];
        foreach ($lastMonthResults as $item) {
            $lastMonthData[$item->line_id] = $item;
        }

        return response()->json([
            'data' => $results->map(function($item) use ($lastMonthData) {
                $lastMonthItem = $lastMonthData[$item->line_id] ?? null;
                return [
                    'line_id' => $item->line_id,
                    'nom_fr' => $item->line_name_fr,
                    'nom_en' => $item->line_name_en,
                    'nom_ar' => $item->line_name_ar,
                    'code' => $item->line_code,
                    'all_times' => [
                        'subscriber_count' => (int)$item->subscriber_count,
                        'total_amount' => (float)$item->total_amount
                    ],
                    'last_month' => [
                        'subscriber_count' => $lastMonthItem ? (int)$lastMonthItem->subscriber_count : 0,
                        'total_amount' => $lastMonthItem ? (float)$lastMonthItem->total_amount : 0
                    ]
                ];
            }),
            'summary' => [
                'all_times' => [
                    'total_subscribers' => (int)$totalSubscribers,
                    'total_amount' => (float)$totalAmount,
                    'total_lines' => $results->count()
                ],
                'last_month' => [
                    'total_subscribers' => (int)$lastMonthTotalSubscribers,
                    'total_amount' => (float)$lastMonthTotalAmount,
                    'total_lines' => $lastMonthResults->count()
                ]
            ]
        ]);
    }

    public function subscribersByAgency(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|integer',
            'agency' => 'nullable|exists:agencies,id',
        ]);

        $query = Agency::leftJoin('sale_points', 'agencies.id', '=', 'sale_points.id_agency')
            ->leftJoin('transactions', function($join) use ($request) {
                $join->on('sale_points.id', '=', 'transactions.sale_point_id')
                     ->where('transactions.status', '=', 'completed');

                if ($request->filled('startDate')) {
                    $join->where('transactions.payment_date', '>=', $request->input('startDate'));
                }
                if ($request->filled('endDate')) {
                    $join->where('transactions.payment_date', '<=', $request->input('endDate'));
                }
            })
            ->leftJoin('subscriptions', function($join) use ($request) {
                $join->on('transactions.subscription_id', '=', 'subscriptions.id');

                if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
                    $join->where('subscriptions.id_subs_type', '=', $request->input('subscriptionType'));
                }
            });

        if ($request->filled('agency') && $request->input('agency') !== 'all') {
            $query->where('agencies.id', $request->input('agency'));
        }

        // TODO: Add sale period filter when sale period relationship is clarified
        // if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
        //     $query->where('transactions.id_sale_period', $request->input('salePeriod'));
        // }

        $results = $query->select(
                'agencies.id as agency_id',
                'agencies.nom_fr as agency_name_fr',
                'agencies.nom_en as agency_name_en',
                'agencies.nom_ar as agency_name_ar',
                'agencies.code as agency_code',
                DB::raw('COUNT(DISTINCT subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('agencies.id', 'agencies.nom_fr', 'agencies.nom_en', 'agencies.nom_ar', 'agencies.code')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $lastMonthStart = now()->startOfMonth()->format('Y-m-d');
        $lastMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $lastMonthQuery = Agency::leftJoin('sale_points', 'agencies.id', '=', 'sale_points.id_agency')
            ->leftJoin('transactions', function($join) use ($lastMonthStart, $lastMonthEnd, $request) {
                $join->on('sale_points.id', '=', 'transactions.sale_point_id')
                     ->where('transactions.status', '=', 'completed')
                     ->whereBetween('transactions.payment_date', [$lastMonthStart, $lastMonthEnd]);
            })
            ->leftJoin('subscriptions', function($join) use ($request) {
                $join->on('transactions.subscription_id', '=', 'subscriptions.id');

                if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
                    $join->where('subscriptions.id_subs_type', '=', $request->input('subscriptionType'));
                }
            });

        if ($request->filled('agency') && $request->input('agency') !== 'all') {
            $lastMonthQuery->where('agencies.id', $request->input('agency'));
        }

        $lastMonthResults = $lastMonthQuery->select(
                'agencies.id as agency_id',
                'agencies.nom_fr as agency_name_fr',
                'agencies.nom_en as agency_name_en',
                'agencies.nom_ar as agency_name_ar',
                'agencies.code as agency_code',
                DB::raw('COUNT(DISTINCT subscriptions.id) as subscriber_count'),
                DB::raw('COALESCE(SUM(transactions.amount), 0) as total_amount')
            )
            ->groupBy('agencies.id', 'agencies.nom_fr', 'agencies.nom_en', 'agencies.nom_ar', 'agencies.code')
            ->orderBy('subscriber_count', 'desc')
            ->get();

        $totalAmount = $results->sum('total_amount');
        $totalSubscribers = $results->sum('subscriber_count');
        $lastMonthTotalAmount = $lastMonthResults->sum('total_amount');
        $lastMonthTotalSubscribers = $lastMonthResults->sum('subscriber_count');

        $lastMonthData = [];
        foreach ($lastMonthResults as $item) {
            $lastMonthData[$item->agency_id] = $item;
        }

        return response()->json([
            'data' => $results->map(function($item) use ($lastMonthData) {
                $lastMonthItem = $lastMonthData[$item->agency_id] ?? null;
                return [
                    'agency_id' => $item->agency_id,
                    'nom_fr' => $item->agency_name_fr,
                    'nom_en' => $item->agency_name_en,
                    'nom_ar' => $item->agency_name_ar,
                    'code' => $item->agency_code,
                    'all_times' => [
                        'subscriber_count' => (int)$item->subscriber_count,
                        'total_amount' => (float)$item->total_amount
                    ],
                    'last_month' => [
                        'subscriber_count' => $lastMonthItem ? (int)$lastMonthItem->subscriber_count : 0,
                        'total_amount' => $lastMonthItem ? (float)$lastMonthItem->total_amount : 0
                    ]
                ];
            }),
            'summary' => [
                'all_times' => [
                    'total_subscribers' => (int)$totalSubscribers,
                    'total_amount' => (float)$totalAmount,
                    'total_agencies' => $results->count()
                ],
                'last_month' => [
                    'total_subscribers' => (int)$lastMonthTotalSubscribers,
                    'total_amount' => (float)$lastMonthTotalAmount,
                    'total_agencies' => $lastMonthResults->count()
                ]
            ]
        ]);
    }

    public function revenuesByClientGovernorate(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
        ]);

        $query = "
            SELECT
                g.id as governorate_id,
                g.nom_fr as governorate_nom_fr,
                g.nom_en as governorate_nom_en,
                g.nom_ar as governorate_nom_ar,
                COUNT(DISTINCT t.payment_id) as nombre_transactions,
                COUNT(DISTINCT s.id) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total
            FROM governorates g
            LEFT JOIN clients c ON g.id = c.id_governorate
            LEFT JOIN subscriptions s ON c.id = s.id_client
            LEFT JOIN transactions t ON s.id = t.subscription_id AND t.status = 'completed'
        ";

        $bindings = [];
        $conditions = [];

        if ($request->filled('startDate')) {
            $conditions[] = "t.payment_date >= ?";
            $bindings[] = $request->input('startDate');
        }

        if ($request->filled('endDate')) {
            $conditions[] = "t.payment_date <= ?";
            $bindings[] = $request->input('endDate');
        }

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $conditions[] = "s.id_subs_type = ?";
            $bindings[] = $request->input('subscriptionType');
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }

        $query .= "
            GROUP BY g.id, g.nom_fr, g.nom_en, g.nom_ar
            ORDER BY g.nom_fr
        ";

        $results = DB::select($query, $bindings);

        $lastMonthQuery = "
            SELECT
                g.id as governorate_id,
                g.nom_fr as governorate_nom_fr,
                g.nom_en as governorate_nom_en,
                g.nom_ar as governorate_nom_ar,
                COUNT(DISTINCT t.payment_id) as nombre_transactions,
                COUNT(DISTINCT s.id) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total
            FROM governorates g
            LEFT JOIN clients c ON g.id = c.id_governorate
            LEFT JOIN subscriptions s ON c.id = s.id_client
            LEFT JOIN transactions t ON s.id = t.subscription_id AND t.status = 'completed'
        ";

        $lastMonthStart = now()->startOfMonth()->format('Y-m-d');
        $lastMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $lastMonthConditions = [];
        $lastMonthBindings = [];

        $lastMonthConditions[] = "t.payment_date >= ?";
        $lastMonthBindings[] = $lastMonthStart;
        $lastMonthConditions[] = "t.payment_date <= ?";
        $lastMonthBindings[] = $lastMonthEnd;

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $lastMonthConditions[] = "s.id_subs_type = ?";
            $lastMonthBindings[] = $request->input('subscriptionType');
        }

        $lastMonthQuery .= " WHERE " . implode(' AND ', $lastMonthConditions);
        $lastMonthQuery .= "
            GROUP BY g.id, g.nom_fr, g.nom_en, g.nom_ar
            ORDER BY g.nom_fr
        ";

        $lastMonthResults = DB::select($lastMonthQuery, $lastMonthBindings);

        $totalTransactions = array_sum(array_column($results, 'nombre_transactions'));
        $totalAbonnements = array_sum(array_column($results, 'nombre_abonnements'));
        $totalMontant = array_sum(array_column($results, 'montant_total'));

        $lastMonthTotalTransactions = array_sum(array_column($lastMonthResults, 'nombre_transactions'));
        $lastMonthTotalAbonnements = array_sum(array_column($lastMonthResults, 'nombre_abonnements'));
        $lastMonthTotalMontant = array_sum(array_column($lastMonthResults, 'montant_total'));

        $lastMonthData = [];
        foreach ($lastMonthResults as $item) {
            $lastMonthData[$item->governorate_id] = $item;
        }

        return response()->json([
            'data' => array_map(function($item) use ($lastMonthData) {
                $lastMonthItem = $lastMonthData[$item->governorate_id] ?? null;
                return [
                    'governorate_id' => $item->governorate_id,
                    'governorate_nom_fr' => $item->governorate_nom_fr,
                    'governorate_nom_en' => $item->governorate_nom_en,
                    'governorate_nom_ar' => $item->governorate_nom_ar,
                    'all_times' => [
                        'nombre_transactions' => (int)$item->nombre_transactions,
                        'nombre_abonnements' => (int)$item->nombre_abonnements,
                        'montant_total' => round((float)$item->montant_total, 2)
                    ],
                    'last_month' => [
                        'nombre_transactions' => $lastMonthItem ? (int)$lastMonthItem->nombre_transactions : 0,
                        'nombre_abonnements' => $lastMonthItem ? (int)$lastMonthItem->nombre_abonnements : 0,
                        'montant_total' => $lastMonthItem ? round((float)$lastMonthItem->montant_total, 2) : 0
                    ]
                ];
            }, $results),
            'summary' => [
                'all_times' => [
                    'total_governorates' => count($results),
                    'total_transactions' => $totalTransactions,
                    'total_abonnements' => $totalAbonnements,
                    'total_montant' => round($totalMontant, 2)
                ],
                'last_month' => [
                    'total_governorates' => count($lastMonthResults),
                    'total_transactions' => $lastMonthTotalTransactions,
                    'total_abonnements' => $lastMonthTotalAbonnements,
                    'total_montant' => round($lastMonthTotalMontant, 2)
                ]
            ]
        ]);
    }

    public function revenuesByPaymentMethod(Request $request): JsonResponse
    {
        $request->validate([
            'subscriptionType' => 'nullable|exists:subs_types,id',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|exists:sale_periods,id',
            'salePoint' => 'nullable|exists:sale_points,id',
        ]);

        $query = "
            SELECT
                pm.id as payment_method_id,
                pm.nom_fr as payment_method_nom_fr,
                pm.nom_en as payment_method_nom_en,
                pm.nom_ar as payment_method_nom_ar,
                COUNT(DISTINCT t.payment_id) as nombre_transactions,
                COUNT(DISTINCT s.id) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total,
                COALESCE(SUM(JSON_EXTRACT(t.payment_details, '$.card_fee')), 0) as montant_frais_carte
            FROM payment_methods pm
            LEFT JOIN transactions t ON pm.id = t.payment_method_id AND t.status = 'completed'
            LEFT JOIN subscriptions s ON t.subscription_id = s.id
        ";

        $bindings = [];
        $conditions = [];

        if ($request->filled('startDate')) {
            $conditions[] = "t.payment_date >= ?";
            $bindings[] = $request->input('startDate');
        }

        if ($request->filled('endDate')) {
            $conditions[] = "t.payment_date <= ?";
            $bindings[] = $request->input('endDate');
        }

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $conditions[] = "s.id_subs_type = ?";
            $bindings[] = $request->input('subscriptionType');
        }

        if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
            $conditions[] = "t.id_sale_period = ?";
            $bindings[] = $request->input('salePeriod');
        }

        if ($request->filled('salePoint') && $request->input('salePoint') !== 'all') {
            $conditions[] = "t.sale_point_id = ?";
            $bindings[] = $request->input('salePoint');
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }

        $query .= "
            GROUP BY pm.id, pm.nom_fr, pm.nom_en, pm.nom_ar
            ORDER BY pm.nom_fr
        ";

        $results = DB::select($query, $bindings);

        $lastMonthQuery = "
            SELECT
                pm.id as payment_method_id,
                pm.nom_fr as payment_method_nom_fr,
                pm.nom_en as payment_method_nom_en,
                pm.nom_ar as payment_method_nom_ar,
                COUNT(DISTINCT t.payment_id) as nombre_transactions,
                COUNT(DISTINCT s.id) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total,
                COALESCE(SUM(JSON_EXTRACT(t.payment_details, '$.card_fee')), 0) as montant_frais_carte
            FROM payment_methods pm
            LEFT JOIN transactions t ON pm.id = t.payment_method_id AND t.status = 'completed'
            LEFT JOIN subscriptions s ON t.subscription_id = s.id
        ";

        $lastMonthStart = now()->startOfMonth()->format('Y-m-d');
        $lastMonthEnd = now()->endOfMonth()->format('Y-m-d');

        $lastMonthConditions = [];
        $lastMonthBindings = [];

        $lastMonthConditions[] = "t.payment_date >= ?";
        $lastMonthBindings[] = $lastMonthStart;
        $lastMonthConditions[] = "t.payment_date <= ?";
        $lastMonthBindings[] = $lastMonthEnd;

        if ($request->filled('subscriptionType') && $request->input('subscriptionType') !== 'all') {
            $lastMonthConditions[] = "s.id_subs_type = ?";
            $lastMonthBindings[] = $request->input('subscriptionType');
        }

        if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
            $lastMonthConditions[] = "t.id_sale_period = ?";
            $lastMonthBindings[] = $request->input('salePeriod');
        }

        if ($request->filled('salePoint') && $request->input('salePoint') !== 'all') {
            $lastMonthConditions[] = "t.sale_point_id = ?";
            $lastMonthBindings[] = $request->input('salePoint');
        }

        $lastMonthQuery .= " WHERE " . implode(' AND ', $lastMonthConditions);
        $lastMonthQuery .= "
            GROUP BY pm.id, pm.nom_fr, pm.nom_en, pm.nom_ar
            ORDER BY pm.nom_fr
        ";

        $lastMonthResults = DB::select($lastMonthQuery, $lastMonthBindings);

        $totalTransactions = array_sum(array_column($results, 'nombre_transactions'));
        $totalAbonnements = array_sum(array_column($results, 'nombre_abonnements'));
        $totalMontant = array_sum(array_column($results, 'montant_total'));
        $totalFraisCartes = array_sum(array_column($results, 'montant_frais_carte'));

        $lastMonthTotalTransactions = array_sum(array_column($lastMonthResults, 'nombre_transactions'));
        $lastMonthTotalAbonnements = array_sum(array_column($lastMonthResults, 'nombre_abonnements'));
        $lastMonthTotalMontant = array_sum(array_column($lastMonthResults, 'montant_total'));
        $lastMonthTotalFraisCartes = array_sum(array_column($lastMonthResults, 'montant_frais_carte'));

        $lastMonthData = [];
        foreach ($lastMonthResults as $item) {
            $lastMonthData[$item->payment_method_id] = $item;
        }

        return response()->json([
            'data' => array_map(function($item) use ($lastMonthData) {
                $lastMonthItem = $lastMonthData[$item->payment_method_id] ?? null;
                return [
                    'payment_method_id' => $item->payment_method_id,
                    'payment_method_nom_fr' => $item->payment_method_nom_fr,
                    'payment_method_nom_en' => $item->payment_method_nom_en,
                    'payment_method_nom_ar' => $item->payment_method_nom_ar,
                    'all_times' => [
                        'nombre_transactions' => (int)$item->nombre_transactions,
                        'nombre_abonnements' => (int)$item->nombre_abonnements,
                        'montant_total' => round((float)$item->montant_total, 2),
                        'montant_frais_carte' => round((float)$item->montant_frais_carte, 2)
                    ],
                    'last_month' => [
                        'nombre_transactions' => $lastMonthItem ? (int)$lastMonthItem->nombre_transactions : 0,
                        'nombre_abonnements' => $lastMonthItem ? (int)$lastMonthItem->nombre_abonnements : 0,
                        'montant_total' => $lastMonthItem ? round((float)$lastMonthItem->montant_total, 2) : 0,
                        'montant_frais_carte' => $lastMonthItem ? round((float)$lastMonthItem->montant_frais_carte, 2) : 0
                    ]
                ];
            }, $results),
            'summary' => [
                'all_times' => [
                    'total_payment_methods' => count($results),
                    'total_transactions' => $totalTransactions,
                    'total_abonnements' => $totalAbonnements,
                    'total_montant' => round($totalMontant, 2),
                    'total_frais_carte' => round($totalFraisCartes, 2)
                ],
                'last_month' => [
                    'total_payment_methods' => count($lastMonthResults),
                    'total_transactions' => $lastMonthTotalTransactions,
                    'total_abonnements' => $lastMonthTotalAbonnements,
                    'total_montant' => round($lastMonthTotalMontant, 2),
                    'total_frais_carte' => round($lastMonthTotalFraisCartes, 2)
                ]
            ]
        ]);
    }

    public function subscriptionsBySubsType(Request $request): JsonResponse
    {
        $request->validate([
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'salePeriod' => 'nullable|exists:sale_periods,id',
            'salePoint' => 'nullable|exists:sale_points,id',
        ]);

        $query = "
            SELECT
                base.subs_type_id,
                base.subs_type_nom_fr,
                base.subs_type_nom_en,
                base.subs_type_nom_ar,
                base.is_impersonal,
                base.is_moral_type,
                base.type_display_name,
                COALESCE(COUNT(DISTINCT CASE WHEN t.payment_id IS NOT NULL THEN t.payment_id END), 0) as nombre_transactions,
                COALESCE(COUNT(DISTINCT CASE WHEN s.id IS NOT NULL THEN s.id END), 0) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total,
                COALESCE(SUM(JSON_EXTRACT(t.payment_details, '$.card_fee')), 0) as montant_frais_carte
            FROM (
                SELECT
                    st.id as subs_type_id,
                    st.nom_fr as subs_type_nom_fr,
                    st.nom_en as subs_type_nom_en,
                    st.nom_ar as subs_type_nom_ar,
                    st.is_impersonal,
                    CASE
                        WHEN st.is_impersonal = 1 AND moral_types.is_moral = 0 THEN 0
                        WHEN st.is_impersonal = 1 AND moral_types.is_moral = 1 THEN 1
                        ELSE NULL
                    END as is_moral_type,
                    CASE
                        WHEN st.is_impersonal = 1 AND moral_types.is_moral = 0 THEN 'Impersonnel Physique'
                        WHEN st.is_impersonal = 1 AND moral_types.is_moral = 1 THEN 'Impersonnel Moral'
                        ELSE st.nom_fr
                    END as type_display_name
                FROM subs_types st
                CROSS JOIN (SELECT 0 as is_moral UNION SELECT 1 as is_moral) as moral_types
                WHERE st.is_impersonal = 1
                UNION
                SELECT
                    st.id as subs_type_id,
                    st.nom_fr as subs_type_nom_fr,
                    st.nom_en as subs_type_nom_en,
                    st.nom_ar as subs_type_nom_ar,
                    st.is_impersonal,
                    NULL as is_moral_type,
                    st.nom_fr as type_display_name
                FROM subs_types st
                WHERE st.is_impersonal = 0
            ) as base
            LEFT JOIN subscriptions s ON base.subs_type_id = s.id_subs_type
            LEFT JOIN clients c ON s.id_client = c.id
            LEFT JOIN transactions t ON s.id = t.subscription_id AND t.status = 'completed'
        ";

        $bindings = [];
        $conditions = [];

        if ($request->filled('startDate')) {
            $conditions[] = "t.payment_date >= ?";
            $bindings[] = $request->input('startDate');
        }

        if ($request->filled('endDate')) {
            $conditions[] = "t.payment_date <= ?";
            $bindings[] = $request->input('endDate');
        }

        if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
            $conditions[] = "t.id_sale_period = ?";
            $bindings[] = $request->input('salePeriod');
        }

        if ($request->filled('salePoint') && $request->input('salePoint') !== 'all') {
            $conditions[] = "t.sale_point_id = ?";
            $bindings[] = $request->input('salePoint');
        }

        $conditions[] = "(base.is_moral_type IS NULL OR c.is_moral = base.is_moral_type)";

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }

        $query .= "
            GROUP BY base.subs_type_id, base.subs_type_nom_fr, base.subs_type_nom_en, base.subs_type_nom_ar, base.is_impersonal, base.is_moral_type, base.type_display_name
            ORDER BY base.subs_type_nom_fr, base.is_moral_type
        ";

        $results = DB::select($query, $bindings);

        $lastMonthQuery = "
            SELECT
                base.subs_type_id,
                base.subs_type_nom_fr,
                base.subs_type_nom_en,
                base.subs_type_nom_ar,
                base.is_impersonal,
                base.is_moral_type,
                base.type_display_name,
                COALESCE(COUNT(DISTINCT CASE WHEN t.payment_id IS NOT NULL THEN t.payment_id END), 0) as nombre_transactions,
                COALESCE(COUNT(DISTINCT CASE WHEN s.id IS NOT NULL THEN s.id END), 0) as nombre_abonnements,
                COALESCE(SUM(t.amount), 0) as montant_total,
                COALESCE(SUM(JSON_EXTRACT(t.payment_details, '$.card_fee')), 0) as montant_frais_carte
            FROM (
                SELECT
                    st.id as subs_type_id,
                    st.nom_fr as subs_type_nom_fr,
                    st.nom_en as subs_type_nom_en,
                    st.nom_ar as subs_type_nom_ar,
                    st.is_impersonal,
                    CASE
                        WHEN st.is_impersonal = 1 AND moral_types.is_moral = 0 THEN 0
                        WHEN st.is_impersonal = 1 AND moral_types.is_moral = 1 THEN 1
                        ELSE NULL
                    END as is_moral_type,
                    CASE
                        WHEN st.is_impersonal = 1 AND moral_types.is_moral = 0 THEN 'Impersonnel Physique'
                        WHEN st.is_impersonal = 1 AND moral_types.is_moral = 1 THEN 'Impersonnel Moral'
                        ELSE st.nom_fr
                    END as type_display_name
                FROM subs_types st
                CROSS JOIN (SELECT 0 as is_moral UNION SELECT 1 as is_moral) as moral_types
                WHERE st.is_impersonal = 1
                UNION
                SELECT
                    st.id as subs_type_id,
                    st.nom_fr as subs_type_nom_fr,
                    st.nom_en as subs_type_nom_en,
                    st.nom_ar as subs_type_nom_ar,
                    st.is_impersonal,
                    NULL as is_moral_type,
                    st.nom_fr as type_display_name
                FROM subs_types st
                WHERE st.is_impersonal = 0
            ) as base
            LEFT JOIN subscriptions s ON base.subs_type_id = s.id_subs_type
            LEFT JOIN clients c ON s.id_client = c.id
            LEFT JOIN transactions t ON s.id = t.subscription_id AND t.status = 'completed'
        ";

        $currentMonthStart = now()->startOfMonth()->format('Y-m-d');
        $currentMonthEnd = now()->endOfMonth()->format('Y-m-d');
        $lastMonthBindings = [];
        $lastMonthConditions = [];

        $lastMonthConditions[] = "(
            (t.payment_date >= ? AND t.payment_date <= ?) OR
            (s.created_at >= ? AND s.created_at <= ? AND t.payment_date IS NULL)
        )";
        $lastMonthBindings[] = $currentMonthStart;
        $lastMonthBindings[] = $currentMonthEnd;
        $lastMonthBindings[] = $currentMonthStart;
        $lastMonthBindings[] = $currentMonthEnd;

        if ($request->filled('salePeriod') && $request->input('salePeriod') !== 'all') {
            $lastMonthConditions[] = "t.id_sale_period = ?";
            $lastMonthBindings[] = $request->input('salePeriod');
        }

        if ($request->filled('salePoint') && $request->input('salePoint') !== 'all') {
            $lastMonthConditions[] = "t.sale_point_id = ?";
            $lastMonthBindings[] = $request->input('salePoint');
        }

        $lastMonthConditions[] = "(base.is_moral_type IS NULL OR c.is_moral = base.is_moral_type)";

        $lastMonthQuery .= " WHERE " . implode(' AND ', $lastMonthConditions);
        $lastMonthQuery .= "
            GROUP BY base.subs_type_id, base.subs_type_nom_fr, base.subs_type_nom_en, base.subs_type_nom_ar, base.is_impersonal, base.is_moral_type, base.type_display_name
            ORDER BY base.subs_type_nom_fr, base.is_moral_type
        ";

        $lastMonthResults = DB::select($lastMonthQuery, $lastMonthBindings);

        $totalTransactions = array_sum(array_column($results, 'nombre_transactions'));
        $totalAbonnements = array_sum(array_column($results, 'nombre_abonnements'));
        $totalMontant = array_sum(array_column($results, 'montant_total'));
        $totalFraisCartes = array_sum(array_column($results, 'montant_frais_carte'));

        $lastMonthTotalTransactions = array_sum(array_column($lastMonthResults, 'nombre_transactions'));
        $lastMonthTotalAbonnements = array_sum(array_column($lastMonthResults, 'nombre_abonnements'));
        $lastMonthTotalMontant = array_sum(array_column($lastMonthResults, 'montant_total'));
        $lastMonthTotalFraisCartes = array_sum(array_column($lastMonthResults, 'montant_frais_carte'));

        $lastMonthData = [];
        foreach ($lastMonthResults as $item) {
            $key = $item->subs_type_id . '_' . ($item->is_impersonal ? $item->is_moral_type : 'regular');
            $lastMonthData[$key] = $item;
        }

        return response()->json([
            'data' => array_map(function($item) use ($lastMonthData) {
                $key = $item->subs_type_id . '_' . ($item->is_impersonal ? $item->is_moral_type : 'regular');
                $lastMonthItem = $lastMonthData[$key] ?? null;

                return [
                    'subs_type_id' => $item->subs_type_id,
                    'subs_type_nom_fr' => $item->subs_type_nom_fr,
                    'subs_type_nom_en' => $item->subs_type_nom_en,
                    'subs_type_nom_ar' => $item->subs_type_nom_ar,
                    'is_impersonal' => (bool)$item->is_impersonal,
                    'is_moral_type' => $item->is_moral_type,
                    'type_display_name' => $item->type_display_name,
                    'all_times' => [
                        'nombre_transactions' => (int)$item->nombre_transactions,
                        'nombre_abonnements' => (int)$item->nombre_abonnements,
                        'montant_total' => round((float)$item->montant_total, 2),
                        'montant_frais_carte' => round((float)$item->montant_frais_carte, 2)
                    ],
                    'last_month' => [
                        'nombre_transactions' => $lastMonthItem ? (int)$lastMonthItem->nombre_transactions : 0,
                        'nombre_abonnements' => $lastMonthItem ? (int)$lastMonthItem->nombre_abonnements : 0,
                        'montant_total' => $lastMonthItem ? round((float)$lastMonthItem->montant_total, 2) : 0,
                        'montant_frais_carte' => $lastMonthItem ? round((float)$lastMonthItem->montant_frais_carte, 2) : 0
                    ]
                ];
            }, $results),
            'summary' => [
                'all_times' => [
                    'total_subs_types' => count($results),
                    'total_transactions' => $totalTransactions,
                    'total_abonnements' => $totalAbonnements,
                    'total_montant' => round($totalMontant, 2),
                    'total_frais_carte' => round($totalFraisCartes, 2)
                ],
                'last_month' => [
                    'total_subs_types' => count($lastMonthResults),
                    'total_transactions' => $lastMonthTotalTransactions,
                    'total_abonnements' => $lastMonthTotalAbonnements,
                    'total_montant' => round($lastMonthTotalMontant, 2),
                    'total_frais_carte' => round($lastMonthTotalFraisCartes, 2)
                ]
            ]
        ]);
    }
}